import { NextRequest, NextResponse } from "next/server";
import { createClient } from "@/lib/supabase-server";
import { createApiHandler } from "@/utils/apiErrorHandler";
import { getResumeTemplateById } from "@/utils/resume-templates/resumeTemplates";
import { fillResumeTemplate } from "@/utils/template-engine";
import { StructuredResumeData } from "@/types/resume-structured";

async function handleGet(_req: NextRequest, { params }: { params: { id: string } }) {
  const supabase = await createClient();
  const { id } = await params;
  const { data, error } = await supabase.from("resumes").select("*").eq("id", id).single();
  if (error) {
    return NextResponse.json({ error: error.message }, { status: 404 });
  }
  return NextResponse.json(data);
}

async function handlePut(req: NextRequest, { params }: { params: { id: string } }) {
  const supabase = await createClient();
  const body = (await req.json()) as {
    structured_data: StructuredResumeData;
    template_id?: string;
  };

  const { id } = await params;

  // Get current user
  const { data: { user }, error: authError } = await supabase.auth.getUser();
  if (authError || !user) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  // Validate required data
  if (!body.structured_data) {
    return NextResponse.json({ error: "structured_data is required" }, { status: 400 });
  }

  if (!body.template_id) {
    return NextResponse.json({ error: "template_id is required" }, { status: 400 });
  }

  try {
    // Generate HTML from structured data using template engine
    const template = getResumeTemplateById(body.template_id);
    if (!template) {
      return NextResponse.json({ error: "Invalid template_id provided" }, { status: 400 });
    }

    const html = fillResumeTemplate(template, body.structured_data);
    if (!html) {
      return NextResponse.json({ error: "Failed to generate HTML from template" }, { status: 500 });
    }

    // Update the resume with structured data, HTML, and template ID
    const { error } = await supabase
      .from("resumes")
      .update({
        structured_data: body.structured_data,
        html: html,
        template_id: body.template_id,
        updated_at: new Date().toISOString()
      })
      .eq("id", id)
      .eq("user_id", user.id); // Ensure user can only update their own resumes

    if (error) {
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    return NextResponse.json({
      success: true,
      message: "Resume updated successfully",
      html_generated: !!html,
      template_id: body.template_id,
      template_name: template.name
    });
  } catch (error) {
    console.error('Error updating resume:', error);
    return NextResponse.json({ error: "Failed to update resume" }, { status: 500 });
  }
}

export const GET = createApiHandler("resume-get", handleGet);
export const PUT = createApiHandler("resume-update", handlePut);
