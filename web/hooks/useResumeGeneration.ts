import { authPost, authPostFormData } from "@/lib/authFetch";
import { useState, useEffect, useRef } from 'react';
import { createClient } from '@/lib/supabase';
import { v4 as uuidv4 } from "uuid";
import { ResumeTemplate, getResumeTemplateById } from "@/utils/resume-templates/resumeTemplates";
import { fillResumeTemplate } from '@/utils/template-engine';
import { StructuredResumeData } from '@/types/resume-structured';
import type { RealtimePostgresChangesPayload, RealtimeChannel } from '@supabase/supabase-js';

/**
 * Very thin client-side wrapper around the Step 3 résumé generation API.
 * This intentionally keeps business logic on the server – the hook merely
 * starts a generation job and polls for its status.
 */
export type GenerationStatus =
  | {
      status: "idle";
    }
  | {
      status: "processing";
      startedAt?: number;
    }
  | {
      status: "done";
      pdfUrl: string;
      suggestions: string[];
      startedAt: number;
      structuredData?: any; // StructuredResumeData - using any to avoid circular imports
    }
  | {
      status: "error";
      error: string;
      startedAt?: number;
    };


// Types for the simplified parameters
export interface ResumeGenerationParams {
  // Resume method and data
  resumeInputMethod: "upload" | "scratch";
  unauthenticatedResumeFile?: File; // For unauthenticated users
  // For scratch method
  formData?: {
    fullName: string;
    professionalTitle: string;
    professionalSummary: string;
    mostRecentJob: {
      title: string;
      company: string;
      achievements: string;
    };
    skills: string;
  };
  // Job method and data
  jobInputMethod: "text" | "image";
  jobText?: string;
  jobImage?: File;
  // Template selection
  selectedTemplate?: ResumeTemplate;
}

/**
 * Kick off résumé generation. Returns the generation ID, which can be fed
 * into `useGenerationStatus`.
 */
export async function startGeneration(
  params: ResumeGenerationParams
): Promise<string> {
  const formData = new FormData();

  // Handle resume input method
  if (params.resumeInputMethod === "upload") {
    formData.append('resumeInputMethod', 'upload');
    
    if (params.unauthenticatedResumeFile) {
      formData.append('unauthenticatedResumeFile', params.unauthenticatedResumeFile);
    }
  } else if (params.resumeInputMethod === "scratch") {
    // Scratch method - add all manual fields
    formData.append('resumeInputMethod', 'scratch');
    if (params.formData) {
      formData.append('fullName', params.formData.fullName);
      formData.append('professionalTitle', params.formData.professionalTitle);
      formData.append('professionalSummary', params.formData.professionalSummary);
      formData.append('jobTitle', params.formData.mostRecentJob.title);
      formData.append('company', params.formData.mostRecentJob.company);
      formData.append('achievements', params.formData.mostRecentJob.achievements);
      formData.append('skills', params.formData.skills);
    }
  }

  // Handle job input method
  if (params.jobInputMethod === "text") {
    formData.append('jobInputMethod', 'text');
    if (params.jobText) {
      formData.append('jobDescription', params.jobText);
    }
  } else if (params.jobInputMethod === "image") {
    formData.append('jobInputMethod', 'image');
    if (params.jobImage) {
      formData.append('jobImage', params.jobImage);
    }
  }

  // Add selected template
  if (params.selectedTemplate) {
    formData.append('templateId', params.selectedTemplate.id);
  }

  const res = await authPostFormData("/api/resume/generate", formData);

  if (!res.ok) {
    const message = (await res.text()) || "Gagal memulai proses generate.";
    throw new Error(message);
  }

  const data = (await res.json()) as { id: string };
  return data.id;
}

/**
 * Update resume with generated HTML content
 */
async function updateResumeWithGeneratedContent(
  resumeId: string,
  html: string,
  templateId: string
): Promise<void> {
  try {
    const supabase = createClient();

    // Update the resume with generated HTML content
    const { error: updateError } = await supabase
      .from('resumes')
      .update({
        html: html,
        updated_at: new Date().toISOString()
      })
      .eq('id', resumeId);

    if (updateError) {
      console.error('Error updating resume with generated HTML content:', updateError);
      return;
    }

    console.log(`Resume ${resumeId} updated with generated HTML content`);
  } catch (error) {
    console.error('Unexpected error during resume HTML update:', error);
  }
}

/**
 * React hook that subscribes to resume generation status updates via Supabase realtime.
 */
export function useGenerationStatus(id: string | null): GenerationStatus {
  const [status, setStatus] = useState<GenerationStatus>({ status: "idle" });
  const subscriptionRef = useRef<RealtimeChannel | null>(null);

  const transformResumeData = async (resume: any): Promise<GenerationStatus> => {
    if (!resume) return { status: "processing" };

    switch (resume.status) {
      case 'done':
        let structuredData = resume.structured_data ? (typeof resume.structured_data === 'string' ? JSON.parse(resume.structured_data) : resume.structured_data) : undefined;
        let html = resume.html;

        // If we have structured data but missing HTML, generate it
        if (structuredData && !html) {
          try {
            // Get template ID from resume data or default to clean-professional
            const templateId = resume.data?.templateId || 'clean-professional';
            const template = getResumeTemplateById(templateId);

            if (template) {
              html = fillResumeTemplate(template, structuredData);

              // Update the database with generated HTML content
              if (html) {
                await updateResumeWithGeneratedContent(resume.id, html, templateId);
              }
            }
          } catch (error) {
            console.error('Error generating HTML from structured data:', error);
            // Continue with whatever we have
          }
        }

        return {
          status: "done",
          pdfUrl: resume.pdf_url || '',
          suggestions: resume.suggestions ? (typeof resume.suggestions === 'string' ? JSON.parse(resume.suggestions) : resume.suggestions) : [],
          startedAt: resume.started_at ? new Date(resume.started_at).getTime() : Date.now(),
          structuredData: structuredData
        };
      case 'error':
        return {
          status: "error",
          error: resume.error_message || 'Generation failed',
          startedAt: resume.started_at ? new Date(resume.started_at).getTime() : undefined
        };
      case 'processing':
      case 'pending':
        return {
          status: "processing",
          startedAt: resume.started_at ? new Date(resume.started_at).getTime() : undefined
        };
      default:
        return { status: "processing" };
    }
  };

  const fetchInitialStatus = async (resumeId: string) => {
    try {
      const supabase = createClient();

      const { data: resume, error } = await supabase
        .from('resumes')
        .select('*')
        .eq('id', resumeId)
        .single();

      if (error) {
        console.error('Error fetching resume status:', error);
        setStatus({ status: "error", error: error.message });
        return;
      }

      const transformedStatus = await transformResumeData(resume);
      setStatus(transformedStatus);
    } catch (err) {
      console.error('Error fetching initial status:', err);
      setStatus({ status: "error", error: 'Failed to fetch status' });
    }
  };

  useEffect(() => {
    // Cleanup previous subscription if it exists
    if (subscriptionRef.current) {
      const supabase = createClient();
      supabase.removeChannel(subscriptionRef.current);
      subscriptionRef.current = null;
    }

    if (!id) {
      setStatus({ status: "idle" });
      return;
    }

    // Set initial processing state
    setStatus({ status: "processing" });

    // Fetch initial status
    fetchInitialStatus(id);

    // Set up realtime subscription
    const supabase = createClient();

    const subscription = supabase
      .channel(`resume_${id}_changes`)
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'resumes',
          filter: `id=eq.${id}`
        },
        async (payload: RealtimePostgresChangesPayload<any>) => {
          console.log('Realtime resume status change:', payload);

          if (payload.eventType === 'UPDATE') {
            const updatedStatus = await transformResumeData(payload.new);
            setStatus(updatedStatus);
          }
        }
      )
      .subscribe();

    // Store subscription in ref
    subscriptionRef.current = subscription;

    // Cleanup subscription on unmount or id change
    return () => {
      if (subscriptionRef.current) {
        supabase.removeChannel(subscriptionRef.current);
        subscriptionRef.current = null;
      }
    };
  }, [id]);

  return status;
}
