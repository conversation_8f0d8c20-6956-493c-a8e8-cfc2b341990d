/// <reference types="https://deno.land/x/types/index.d.ts" />
declare const Deno: any;

import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import { GoogleGenerativeAI } from "https://esm.sh/@google/generative-ai@0.17.1"

// Import template-related types and utilities
interface StructuredResumeData {
  personalInfo: {
    fullName: string;
    email: string;
    phone: string;
    linkedin?: string;
    location: string;
    website?: string;
    github?: string;
  };
  professionalSummary: string;
  targetPosition: string;
  experiences: Array<{
    id: string;
    jobTitle: string;
    company: string;
    location: string;
    startDate: string;
    endDate: string;
    responsibilities: string[];
  }>;
  education: Array<{
    id: string;
    degree: string;
    institution: string;
    location?: string;
    graduationDate: string;
    gpa?: string;
    relevantCoursework?: string[];
    honors?: string[];
  }>;
  skills: {
    categories: Array<{
      category: string;
      skills: string[];
    }>;
    allSkills?: string[];
  };
  certifications?: Array<{
    id: string;
    name: string;
    issuer: string;
    date: string;
    credentialId?: string;
  }>;
  projects?: Array<{
    id: string;
    title: string;
    description: string;
    technologies: string[];
    link?: string;
    achievements?: string[];
  }>;
  languages?: Array<{
    language: string;
    proficiency: string;
  }>;
  awards?: Array<{
    id: string;
    title: string;
    issuer: string;
    date: string;
    description?: string;
  }>;
  metadata?: {
    generatedAt: string;
    lastModified: string;
    templateId?: string;
    aiSuggestions?: string[];
  };
}


// Types for resume generation
interface ResumeInput {
  file?: {
    buffer?: string; // base64 encoded
    extractedText?: string;
    mimeType: string;
  };
  manual?: {
    fullName: string;
    professionalTitle: string;
    professionalSummary: string;
    mostRecentJob: {
      title: string;
      company: string;
      achievements: string;
    };
    skills: string;
  };
}

interface JobInput {
  description?: string;
  image?: {
    buffer: string; // base64 encoded
    mimeType: string;
  };
}

interface ResumeGenerationResult {
  structuredData: StructuredResumeData;
}

interface GenerateResumeRequest {
  resumeId: string;
  resumeInput: ResumeInput;
  jobInput: JobInput;
  templateId: string;
}

/**
 * Converts a base64 file to a generative part for the AI model
 */
function fileToGenerativePart(base64Data: string, mimeType: string) {
  return {
    inlineData: {
      data: base64Data,
      mimeType
    }
  };
}

/**
 * Generates structured resume data and HTML using the Gemini AI model
 */
async function generateATSResume(
  resumeInput: ResumeInput,
  jobInput: JobInput,
  googleAIKey: string,
  templateId: string = 'clean-professional'
): Promise<ResumeGenerationResult> {
  // Initialize Google AI client
  const genAI = new GoogleGenerativeAI(googleAIKey);
  const model = genAI.getGenerativeModel({ model: "gemini-2.5-flash" });

  // Validate inputs
  if (!resumeInput.file && !resumeInput.manual) {
    throw new Error('Either resume file or manual resume data must be provided');
  }

  if (!jobInput.description && !jobInput.image) {
    throw new Error('Either job description text or job posting image must be provided');
  }

  // Build the prompt parts
  const parts: any[] = [];

  // Add resume information
  if (resumeInput.file) {
    // Validate file type
    const supportedTypes = [
      'application/pdf',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'text/plain',
      'image/png',
      'image/jpeg',
      'image/jpg'
    ];

    console.log('Mime Type:', resumeInput.file.mimeType)

    if (!supportedTypes.includes(resumeInput.file.mimeType)) {
      throw new Error('Unsupported resume file format. Please use PDF, DOCX, PNG, JPG, or JPEG files.');
    }

    if (resumeInput.file.mimeType === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document') {
      // For DOCX files, use the pre-processed extracted text
      if (resumeInput.file.extractedText) {
        parts.push({ text: `Current Resume Content:\n${resumeInput.file.extractedText}` });
      } else {
        throw new Error('DOCX file must include extractedText property');
      }
    } else if (['image/png', 'image/jpeg', 'image/jpg'].includes(resumeInput.file.mimeType)) {
      // For image formats, use the file buffer
      if (!resumeInput.file.buffer) {
        throw new Error('Image files must include buffer property');
      }
      parts.push({ text: 'Current Resume (as image):' });
      parts.push(fileToGenerativePart(resumeInput.file.buffer, resumeInput.file.mimeType));
    } else {
      // For PDF and TXT files, use the file buffer
      if (!resumeInput.file.buffer) {
        throw new Error('PDF and TXT files must include buffer property');
      }
      parts.push({ text: 'Current Resume:' });
      parts.push(fileToGenerativePart(resumeInput.file.buffer, resumeInput.file.mimeType));
    }
  } else if (resumeInput.manual) {
    // Use manual input data
    const manualData = resumeInput.manual;
    parts.push({
      text: `Current Resume Information:
Full Name: ${manualData.fullName}
Professional Title: ${manualData.professionalTitle}
Professional Summary: ${manualData.professionalSummary}
Most Recent Job:
  - Title: ${manualData.mostRecentJob.title}
  - Company: ${manualData.mostRecentJob.company}
  - Achievements: ${manualData.mostRecentJob.achievements}
Skills: ${manualData.skills}`
    });
  }

  // Add job information
  if (jobInput.description) {
    parts.push({ text: `Job Description:\n${jobInput.description}` });
  }

  if (jobInput.image) {
    // Validate image type
    if (!jobInput.image.mimeType.startsWith('image/')) {
      throw new Error('Unsupported job image format. Only image formats are accepted.');
    }

    parts.push({ text: 'Job Posting Image:' });
    parts.push(fileToGenerativePart(jobInput.image.buffer, jobInput.image.mimeType));
  }

  // TODO: hapus batas maksimal kalau udah implement page breaking dengan baik
  // Add the main prompt
  parts.push({
    text: `🚨 **PERSYARATAN BATAS KARAKTER KRITIS** 🚨
  
**SELURUH RESPONS JSON TIDAK BOLEH MELEBIHI 3500 KARAKTER TOTAL**

Ini adalah BATAS KERAS - Anda harus menghitung karakter dan memastikan respons JSON lengkap (termasuk semua kurung, tanda kutip, koma, dan spasi) tetap di bawah 3500 karakter. Sebelum merespons, verifikasi jumlah karakter Anda.

Anda adalah ahli penulisan CV dan pelatih karir dengan pengalaman 15+ tahun membantu profesional bertransisi antar peran dan industri. Tugas Anda adalah membuat CV yang sangat tertarget dan ATS-optimized yang memaksimalkan peluang wawancara untuk kandidat yang melamar pekerjaan berbeda dari latar belakang mereka saat ini.

## PROSES ANALISIS ANDA:

1. **Analisis Persyaratan Pekerjaan**: Ekstraksi keterampilan kunci, kualifikasi, tanggung jawab dari deskripsi pekerjaan. Identifikasi persyaratan wajib vs. opsional.

2. **Analisis Transisi Karir**: Ketika latar belakang kandidat berbeda dari peran target, identifikasi keterampilan yang dapat ditransfer dan pengalaman relevan yang menjembatani kesenjangan.

3. **Pemetaan Kata Kunci**: Identifikasi kata kunci ATS kritis yang harus muncul secara alami dalam CV.

4. **Seleksi Konten Strategis**: Pilih HANYA pengalaman, proyek, dan keterampilan paling relevan yang menunjukkan potensi kandidat untuk peran target.

## BATAS KONTEN KETAT (Optimasi Halaman A4):

**Ringkasan Profesional**: Maksimal 30-50 kata - harus mengatasi transisi karir jika berlaku
**Pengalaman**: HANYA posisi yang paling relevan
**Setiap Poin**: Maksimal 12-18 kata
**Poin per Posisi**: Maksimal 3 poin fokus pada pencapaian yang dapat ditransfer
**Keterampilan**: 2-3 kategori dengan 3-4 keterampilan masing-masing (prioritas yang relevan dengan pekerjaan)
**Proyek**: HANYA proyek yang paling relevan
**Pendidikan**: Info dasar saja kecuali sangat relevan dengan peran target
**Bagian Opsional**: Minimalkan sertifikasi/penghargaan kecuali kritis untuk transisi peran

## OPTIMASI TRANSISI KARIR:

### Untuk Aplikasi Peran Berbeda:
- **Reframe Pengalaman**: Terjemahkan tanggung jawab peran sebelumnya ke bahasa pekerjaan target
- **Sorot Keterampilan yang Dapat Ditransfer**: Tekankan keterampilan yang berlaku antar industri
- **Membangun Jembatan**: Tunjukkan cerita progres karir yang logis meski berganti bidang
- **Mengatasi Gap**: Posisikan perubahan karir sebagai langkah pertumbuhan strategis

### Prioritas Konten:
- Pengalaman paling relevan didahulukan dan diurut berdasarkan yang terbaru, yang tidak relevan diabaikan
- Proyek sampingan/freelance diangkat jika lebih relevan dari pekerjaan utama
- Keterampilan diurutkan berdasarkan relevansi pekerjaan, bukan tingkat keahlian
- Pendidikan lebih ditekankan untuk transisi tingkat entry

## PERSYARATAN OUTPUT:

### Panduan Penulisan:
- Gunakan kata kerja aksi yang dapat diterjemahkan antar industri (mengelola, menganalisis, mengoptimalkan)
- Kuantifikasi dengan angka/persentase jika memungkinkan
- Reframe poin untuk mencocokkan terminologi pekerjaan target
- Present tense untuk peran saat ini, past tense untuk sebelumnya
- Sertakan bahasa penghubung industri dan kata kunci secara alami

### Optimasi ATS & Transisi:
- Sertakan kata kunci persis dari deskripsi pekerjaan
- Gunakan terminologi keterampilan yang dapat ditransfer
- Atasi kekhawatiran potensial secara proaktif dalam ringkasan
- Tunjukkan konteks relevan untuk perubahan karir

**Format Respons:**
⚠️ **INGAT: Total respons JSON harus di bawah 3500 karakter!**

{
  "structuredData": {
    "personalInfo": {
      "fullName": "Nama Lengkap",
      "email": "<EMAIL>", 
      "phone": "Nomor Telepon",
      "location": "Kota, Provinsi/Negara",
      "linkedin": "linkedin.com/in/username"
    },
    "professionalSummary": "40-50 kata mengatasi transisi karir dan kualifikasi kunci untuk peran target",
    "targetPosition": "Judul Pekerjaan dari deskripsi pekerjaan",
    "experiences": [
      {
        "id": "exp-1",
        "jobTitle": "Judul Pekerjaan Paling Relevan",
        "company": "Nama Perusahaan",
        "location": "Kota, Provinsi", 
        "startDate": "MM/YYYY",
        "endDate": "MM/YYYY atau Sekarang",
        "responsibilities": [
          "12-18 kata - pencapaian yang direframe untuk peran target dengan hasil terukur",
          "12-18 kata - demonstrasi keterampilan yang dapat ditransfer dengan dampak spesifik",
          "12-18 kata - pencapaian relevan menggunakan terminologi industri target"
        ]
      }
    ],
    "education": [
      {
        "id": "edu-1",
        "degree": "Gelar",
        "institution": "Nama Universitas", 
        "graduationDate": "MM/YYYY",
        "gpa": "3.8",
        "relevantCoursework": ["Course 1", "Course 2"],
        "honors": ["Penghargaan 1", "Penghargaan 2"]
      }
    ],
    "skills": {
      "categories": [
        {
          "category": "Keterampilan Inti",
          "skills": ["Keterampilan relevan pekerjaan 1", "Keterampilan relevan pekerjaan 2", "Keterampilan relevan pekerjaan 3"]
        },
        {
          "category": "Teknis",
          "skills": ["Keterampilan teknis 1", "Keterampilan teknis 2", "Keterampilan teknis 3"]
        }
      ]
    },
    "projects": [
      {
        "id": "proj-1", 
        "title": "Judul Proyek Paling Relevan",
        "description": "Deskripsi singkat menunjukkan relevansi dengan peran target",
        "technologies": ["Teknologi 1", "Teknologi 2"],
        "achievements": ["Pencapaian utama 1", "Pencapaian utama 2"]
      }
    ],
    "metadata": {
      "transitionStrategy": "Catatan singkat tentang pendekatan perubahan karir",
      "aiSuggestions": ["Saran untuk memperkuat aplikasi", "Saran untuk konten tambahan"]
    }
  }
}

**PANDUAN KHUSUS TRANSISI:**
- **KRITIS**: Di bawah 3500 karakter total - hitung sebelum merespons!
- Fokus pada pengalaman terbaik yang menjembatani ke peran target
- Reframe tanggung jawab menggunakan terminologi pekerjaan target
- Tekankan pencapaian yang dapat ditransfer daripada tugas spesifik industri
- Sertakan proyek paling relevan yang menunjukkan keterampilan yang diinginkan
- Ringkasan profesional harus mengatasi transisi karir dengan percaya diri
- Keterampilan diprioritaskan berdasarkan relevansi pekerjaan, bukan tingkat keahlian saat ini
- **GUNAKAN BAHASA INDONESIA** untuk semua konten CV kecuali field teknis yang umum dalam bahasa Inggris

**PEMERIKSAAN AKHIR**: Hitung total karakter dalam respons JSON - harus di bawah 4000 karakter untuk format A4 yang tepat.`
  });

  // Generate the content using Google AI
  const result = await model.generateContent(parts);
  const response = await result.response;
  const responseText = response.text();

  // Parse the JSON response
  let parsedResponse;
  try {
    // Try to extract JSON from the response
    const jsonMatch = responseText.match(/\{[\s\S]*\}/);
    if (jsonMatch) {
      parsedResponse = JSON.parse(jsonMatch[0]);
    } else {
      throw new Error('No JSON found in response');
    }
  } catch (parseError) {
    console.error('Failed to parse AI response as JSON:', parseError);
    console.error('Response text:', responseText);

    // Fallback: create basic structured data and HTML
    const fallbackStructuredData: StructuredResumeData = {
      personalInfo: {
        fullName: '',
        email: '',
        phone: '',
        location: ''
      },
      professionalSummary: '',
      targetPosition: '',
      experiences: [],
      education: [],
      skills: { categories: [] }
    };

    return {
      structuredData: fallbackStructuredData,
    };
  }

  // Validate the parsed response
  if (!parsedResponse.structuredData || typeof parsedResponse.structuredData !== 'object') {
    throw new Error('Invalid response format: structuredData is missing or not an object');
  }

  return {
    structuredData: parsedResponse.structuredData,
  };
}

/**
 * Main Edge Function handler
 */
serve(async (req: any) => {
  const corsHeaders = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  }

  // Handle CORS preflight
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    console.log('=== Edge Function Started (v2) ===')
    console.log('Request method:', req.method)
    console.log('Request URL:', req.url)

    // Get environment variables
    const supabaseUrl = Deno.env.get('SUPABASE_URL') ?? ''
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    const googleAIKey = Deno.env.get('GOOGLE_AI_API_KEY') ?? ''

    console.log('Environment variables check:')
    console.log('- SUPABASE_URL:', supabaseUrl ? 'SET' : 'MISSING')
    console.log('- SUPABASE_SERVICE_ROLE_KEY:', supabaseServiceKey ? 'SET' : 'MISSING')
    console.log('- GOOGLE_AI_API_KEY:', googleAIKey ? 'SET' : 'MISSING')

    if (!supabaseUrl || !supabaseServiceKey || !googleAIKey) {
      const missingVars = []
      if (!supabaseUrl) missingVars.push('SUPABASE_URL')
      if (!supabaseServiceKey) missingVars.push('SUPABASE_SERVICE_ROLE_KEY')
      if (!googleAIKey) missingVars.push('GOOGLE_AI_API_KEY')

      const errorMsg = `Missing required environment variables: ${missingVars.join(', ')}`
      console.error('Environment error:', errorMsg)
      throw new Error(errorMsg)
    }

    // Create Supabase client
    console.log('Creating Supabase client...')
    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    // Parse request body
    console.log('Parsing request body...')
    console.log('Request headers:', Object.fromEntries(req.headers.entries()))
    console.log('Request method:', req.method)
    console.log('Request URL:', req.url)
    console.log('Request content-type:', req.headers.get('content-type'))

    let requestBody: GenerateResumeRequest
    try {
      // Check if we can clone the request to avoid consuming the body
      let rawBody: string;
      try {
        rawBody = await req.text()
      } catch (bodyError) {
        console.error('Error reading request body:', bodyError)
        throw new Error(`Failed to read request body: ${(bodyError as any).message}`)
      }

      console.log('Raw body length:', rawBody.length)
      console.log('Raw body type:', typeof rawBody)
      console.log('Raw body preview (first 500 chars):', rawBody.substring(0, 500))
      console.log('Raw body ends with (last 100 chars):', rawBody.substring(Math.max(0, rawBody.length - 100)))

      if (!rawBody || rawBody.trim() === '') {
        console.error('Request body is empty or whitespace only')
        console.error('Raw body exact value:', JSON.stringify(rawBody))
        throw new Error('Request body is empty')
      }

      requestBody = JSON.parse(rawBody)
      console.log('Request body parsed successfully')
      console.log('Request body keys:', Object.keys(requestBody))
    } catch (parseError) {
      console.error('Failed to parse request body:', parseError)
      console.error('Parse error details:', {
        name: (parseError as any).name,
        message: (parseError as any).message,
        stack: (parseError as any).stack
      })
      throw new Error(`Failed to parse request body: ${(parseError as any).message}`)
    }

    const { resumeId, resumeInput, jobInput, templateId } = requestBody
    console.log('Request parameters:')
    console.log('- resumeId:', resumeId ? 'PROVIDED' : 'MISSING')
    console.log('- resumeInput:', resumeInput ? 'PROVIDED' : 'MISSING')
    console.log('- jobInput:', jobInput ? 'PROVIDED' : 'MISSING')
    console.log('- templateId:', templateId ? 'PROVIDED' : 'MISSING')

    if (!resumeId || !resumeInput || !jobInput) {
      const missingParams = []
      if (!resumeId) missingParams.push('resumeId')
      if (!resumeInput) missingParams.push('resumeInput')
      if (!jobInput) missingParams.push('jobInput')

      const errorMsg = `Missing required parameters: ${missingParams.join(', ')}`
      console.error('Parameter validation error:', errorMsg)
      throw new Error(errorMsg)
    }

    // Update status to 'processing'
    await supabase
      .from('resumes')
      .update({
        status: 'processing',
        updated_at: new Date().toISOString()
      })
      .eq('id', resumeId)

    try {
      // Generate the resume
      const result = await generateATSResume(resumeInput, jobInput, googleAIKey, templateId || 'clean-professional')

      console.log(`result: ${JSON.stringify(result)}`)

      // Get the original data and add suggestions to it
      const { data: currentData } = await supabase
        .from('resumes')
        .select('data')
        .eq('id', resumeId)
        .single()

      const originalData = currentData?.data || {}
      const updatedData = {
        ...originalData,
        generatedAt: new Date().toISOString(),
        templateId: templateId || 'clean-professional'
      }

      // Update the database with the generated content
      const { error: updateError } = await supabase
        .from('resumes')
        .update({
          data: updatedData,
          structured_data: result.structuredData,
          status: 'done',
          updated_at: new Date().toISOString()
        })
        .eq('id', resumeId)

      if (updateError) {
        throw new Error(`Database update failed: ${updateError.message}`)
      }

      return new Response(
        JSON.stringify({
          success: true,
          resumeId,
          message: 'Resume generated successfully',
          structuredData: result.structuredData,
        }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 200,
        }
      )

    } catch (generationError) {
      console.error('Resume generation failed:', generationError)
      console.error('Error details:', {
        name: (generationError as any).name,
        message: (generationError as any).message,
        stack: (generationError as any).stack
      })

      // Get the original data and add error message to it
      console.log('Updating resume with error status...')
      try {
        const { data: currentData } = await supabase
          .from('resumes')
          .select('data')
          .eq('id', resumeId)
          .single()

        const originalData = currentData?.data || {}
        const errorData = {
          ...originalData,
          error_message: (generationError as any).message,
          errorAt: new Date().toISOString()
        }

        // Update status to 'error' with error message
        await supabase
          .from('resumes')
          .update({
            data: errorData,
            status: 'error',
            updated_at: new Date().toISOString()
          })
          .eq('id', resumeId)

        console.log('Resume error status updated successfully')
      } catch (errorUpdateError) {
        console.error('Failed to update resume error status:', errorUpdateError)
      }

      throw generationError
    }

  } catch (error) {
    console.error('=== Edge Function Error ===')
    console.error('Error type:', typeof error)
    console.error('Error details:', {
      name: (error as any).name,
      message: (error as any).message,
      stack: (error as any).stack
    })

    // Additional error context
    console.error('Error occurred at:', new Date().toISOString())
    console.error('Request URL:', req.url)
    console.error('Request method:', req.method)

    // Log more detailed error information
    if (error instanceof Error) {
      console.error('Error instanceof Error:', true)
      console.error('Error constructor name:', error.constructor.name)
    }

    return new Response(
      JSON.stringify({
        success: false,
        error: (error as any).message || 'Unknown error occurred',
        errorType: (error as any).name || 'UnknownError',
        timestamp: new Date().toISOString()
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500,
      }
    )
  }
})